#!/usr/bin/env python3
"""
Proper Walk-Forward Validation Training Script

This script implements the recommended data splitting approach:
1. Full 4-year dataset (June 2021 - July 2025)
2. Chronological splits with no shuffling
3. Walk-forward validation with rolling windows
4. Reserved out-of-sample data (April 2025 - July 2025)

Data Splitting Strategy:
- Training Period: June 2021 - December 2023 (70% of 4 years)
- Test Period: January 2024 - March 2025 (30% of 4 years) 
- Reserved Out-of-Sample: April 2025 - July 2025 (completely separate)

Walk-Forward Validation:
- 6-month training windows
- 3-month testing windows
- Rolling forward by 3 months each iteration
"""

import asyncio
import os
import sys
import logging
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Any

# Add project root to path
sys.path.append('.')

from app.training.training_orchestrator import TrainingOrchestrator
from app.training.comprehensive_training_system import TrainingConfig
from app.training.walk_forward_validation import WalkForwardValidator, WalkForwardConfig
from app.db.db_executor import DatabaseExecutor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ProperWalkForwardTraining:
    """Implements proper walk-forward validation following time-series best practices"""
    
    def __init__(self, db: DatabaseExecutor):
        self.db = db
        self.orchestrator = TrainingOrchestrator(db, './proper_walkforward_results')
        self.validator = WalkForwardValidator(db, './walkforward_validation_results')
        
        # Data periods following recommendations
        self.full_start = datetime(2021, 6, 1, tzinfo=timezone.utc)
        self.training_end = datetime(2023, 12, 31, tzinfo=timezone.utc)  # 70% cutoff
        self.test_end = datetime(2025, 3, 31, tzinfo=timezone.utc)       # 30% test period
        self.reserved_start = datetime(2025, 4, 1, tzinfo=timezone.utc)  # Reserved out-of-sample
        self.reserved_end = datetime(2025, 7, 31, tzinfo=timezone.utc)
    
    async def verify_data_availability(self) -> Dict[str, Any]:
        """Verify that sufficient data is available for the full 4-year period"""
        logger.info("Verifying data availability for full 4-year period...")
        
        try:
            # Check total data availability
            result = self.db.execute_select(
                """SELECT MIN(timestamp), MAX(timestamp), COUNT(*) 
                   FROM kraken_ohlc WHERE pair = %s""",
                ('SOL/USD',)
            )
            
            if not result or not result[0][0]:
                return {'available': False, 'error': 'No SOL/USD data found'}
            
            min_date, max_date, total_count = result[0]
            
            # Check data in each period
            periods = {
                'training': (self.full_start, self.training_end),
                'testing': (datetime(2024, 1, 1, tzinfo=timezone.utc), self.test_end),
                'reserved': (self.reserved_start, self.reserved_end)
            }
            
            period_counts = {}
            for period_name, (start, end) in periods.items():
                count_result = self.db.execute_select(
                    """SELECT COUNT(*) FROM kraken_ohlc 
                       WHERE pair = %s AND timestamp BETWEEN %s AND %s""",
                    ('SOL/USD', start, end)
                )
                period_counts[period_name] = count_result[0][0] if count_result else 0
            
            logger.info(f"Data availability summary:")
            logger.info(f"  Full range: {min_date} to {max_date}")
            logger.info(f"  Total candles: {total_count}")
            logger.info(f"  Training period: {period_counts['training']} candles")
            logger.info(f"  Testing period: {period_counts['testing']} candles")
            logger.info(f"  Reserved period: {period_counts['reserved']} candles")
            
            # Minimum requirements
            min_training = 8760  # ~1 year of hourly data
            min_testing = 2160   # ~3 months of hourly data
            
            sufficient = (
                period_counts['training'] >= min_training and
                period_counts['testing'] >= min_testing
            )
            
            return {
                'available': sufficient,
                'total_count': total_count,
                'period_counts': period_counts,
                'date_range': (min_date, max_date),
                'sufficient': sufficient
            }
            
        except Exception as e:
            logger.error(f"Error verifying data availability: {e}")
            return {'available': False, 'error': str(e)}
    
    async def run_initial_training(self) -> str:
        """Run initial training on the full training period (June 2021 - Dec 2023)"""
        logger.info("="*60)
        logger.info("PHASE 1: INITIAL TRAINING ON FULL TRAINING PERIOD")
        logger.info("="*60)
        
        config = TrainingConfig(
            pair='SOL/USD',
            start_date=self.full_start,
            end_date=self.training_end,
            
            # PPO hyperparameters
            learning_rate_initial=0.0003,
            learning_rate_final=0.00005,
            n_steps=1024,
            batch_size=128,
            n_epochs=10,
            gamma=0.97,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef_initial=0.05,
            ent_coef_final=0.01,
            vf_coef=0.5,
            max_grad_norm=0.5,
            
            # Training parameters for large dataset
            total_timesteps=100000,  # Increased for 2.5 years of data
            random_seeds=[42],       # Single seed for initial training
            evaluation_frequency=20000,
            save_frequency=50000,
            
            # Chronological splits within training period
            train_ratio=0.8,
            validation_ratio=0.15,
            test_ratio=0.05
        )
        
        logger.info(f"Training on: {config.start_date.date()} to {config.end_date.date()}")
        logger.info(f"Duration: {(config.end_date - config.start_date).days} days")
        
        results = await self.orchestrator.training_system.run_experiment(config)
        
        if results and len(results) > 0:
            best_result = results[0]  # Single seed
            logger.info(f"✅ Initial training completed")
            logger.info(f"   Sharpe Ratio: {best_result.metrics.sharpe_ratio:.4f}")
            logger.info(f"   Total Return: {best_result.metrics.total_return:.2f}%")
            logger.info(f"   Model Path: {best_result.model_path}")
            return best_result.model_path
        else:
            raise ValueError("Initial training failed")
    
    async def run_walk_forward_validation(self, base_model_path: str) -> Dict[str, Any]:
        """Run walk-forward validation with 6-month training, 3-month testing windows"""
        logger.info("="*60)
        logger.info("PHASE 2: WALK-FORWARD VALIDATION")
        logger.info("="*60)
        
        # Walk-forward configuration following recommendations
        wf_config = WalkForwardConfig(
            pair="SOL/USD",
            start_date=self.full_start,
            end_date=self.test_end,
            training_months=6,    # 6-month training windows
            testing_months=3,     # 3-month testing windows
            step_months=3         # Roll forward by 3 months
        )
        
        # Training configuration for each window
        training_config = TrainingConfig(
            pair="SOL/USD",
            learning_rate_initial=0.0003,
            learning_rate_final=0.00005,
            n_steps=1024,
            batch_size=128,
            n_epochs=10,
            gamma=0.97,
            total_timesteps=20000,  # Reduced per window
            random_seeds=[42],      # Single seed for speed
            evaluation_frequency=5000,
            save_frequency=20000
        )
        
        logger.info("Walk-Forward Configuration:")
        logger.info(f"  Training Window: {wf_config.training_months} months")
        logger.info(f"  Testing Window: {wf_config.testing_months} months")
        logger.info(f"  Step Size: {wf_config.step_months} months")
        logger.info(f"  Date Range: {wf_config.start_date.date()} to {wf_config.end_date.date()}")
        
        # Calculate expected number of windows
        total_months = (wf_config.end_date.year - wf_config.start_date.year) * 12 + \
                      (wf_config.end_date.month - wf_config.start_date.month)
        expected_windows = max(0, (total_months - wf_config.training_months) // wf_config.step_months)
        logger.info(f"  Expected Windows: ~{expected_windows}")
        
        # Run validation
        results = await self.validator.run_walk_forward_validation(wf_config, training_config)
        
        # Analyze results
        analysis = self.validator.analyze_walk_forward_results(results)
        
        logger.info("✅ Walk-Forward Validation completed")
        if analysis:
            time_analysis = analysis.get('time_analysis', {})
            stats = analysis.get('statistics', {})
            
            logger.info(f"   Total Periods: {time_analysis.get('total_periods', 0)}")
            logger.info(f"   Positive Periods: {time_analysis.get('positive_periods', 0)}")
            logger.info(f"   Consistency Ratio: {time_analysis.get('consistency_ratio', 0):.2%}")
            
            if 'sharpe_ratio' in stats:
                sharpe = stats['sharpe_ratio']
                logger.info(f"   Avg Sharpe: {sharpe.get('mean', 0):.4f} ± {sharpe.get('std', 0):.4f}")
        
        return analysis
    
    async def test_on_reserved_data(self, best_model_path: str) -> Dict[str, Any]:
        """Test the best model on completely reserved out-of-sample data"""
        logger.info("="*60)
        logger.info("PHASE 3: RESERVED OUT-OF-SAMPLE TESTING")
        logger.info("="*60)
        
        config = TrainingConfig(
            pair='SOL/USD',
            start_date=self.reserved_start,
            end_date=self.reserved_end,
            
            # Use best model for testing (no training)
            total_timesteps=1,  # Minimal - just for testing
            random_seeds=[42],
            
            # Full period for testing
            train_ratio=0.0,
            validation_ratio=0.0,
            test_ratio=1.0
        )
        
        logger.info(f"Testing on reserved data: {config.start_date.date()} to {config.end_date.date()}")
        logger.info("This data was NEVER used during training or validation")
        
        # Load the best model and test on reserved data
        # Note: This would require implementing a test-only mode in the training system
        logger.info("⚠️  Reserved data testing requires additional implementation")
        logger.info("   This would involve loading the trained model and running pure inference")
        
        return {
            'period': (self.reserved_start, self.reserved_end),
            'status': 'configured_but_not_implemented',
            'note': 'Requires test-only mode implementation'
        }
    
    async def run_complete_proper_validation(self) -> Dict[str, Any]:
        """Run the complete proper validation pipeline"""
        start_time = datetime.now()
        
        logger.info("🚀 STARTING PROPER WALK-FORWARD VALIDATION PIPELINE")
        logger.info("Following time-series best practices with 4-year dataset")
        
        # Phase 0: Verify data
        data_status = await self.verify_data_availability()
        if not data_status['available']:
            raise ValueError(f"Insufficient data: {data_status.get('error', 'Unknown error')}")
        
        # Phase 1: Initial training
        best_model_path = await self.run_initial_training()
        
        # Phase 2: Walk-forward validation
        validation_analysis = await self.run_walk_forward_validation(best_model_path)
        
        # Phase 3: Reserved data testing
        reserved_results = await self.test_on_reserved_data(best_model_path)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("="*60)
        logger.info("🎉 PROPER VALIDATION PIPELINE COMPLETED")
        logger.info("="*60)
        logger.info(f"Total Duration: {duration}")
        logger.info(f"Best Model: {best_model_path}")
        
        return {
            'status': 'completed',
            'duration': duration,
            'best_model_path': best_model_path,
            'data_status': data_status,
            'validation_analysis': validation_analysis,
            'reserved_results': reserved_results
        }


async def main():
    """Main execution function"""
    logger.info("🎯 PROPER WALK-FORWARD VALIDATION TRAINING")
    logger.info("Implementing recommended time-series data splitting")
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create and run proper validation
        proper_training = ProperWalkForwardTraining(db)
        results = await proper_training.run_complete_proper_validation()
        
        if results['status'] == 'completed':
            logger.info("\n✅ SUCCESS: Proper validation completed!")
            logger.info("This approach follows time-series best practices:")
            logger.info("  ✅ 4-year dataset utilized")
            logger.info("  ✅ Chronological splits maintained")
            logger.info("  ✅ Walk-forward validation implemented")
            logger.info("  ✅ Reserved out-of-sample data preserved")
        else:
            logger.error("❌ Validation failed")
            
    except Exception as e:
        logger.error(f"Pipeline failed: {e}")
        logger.error("Full error details:", exc_info=True)
        
    finally:
        db.close()


if __name__ == "__main__":
    asyncio.run(main())
