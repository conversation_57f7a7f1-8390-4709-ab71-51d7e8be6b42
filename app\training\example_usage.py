"""
Example Usage of the Comprehensive Training System

This script demonstrates how to use the comprehensive training system
for different scenarios and provides practical examples.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(project_root)

from app.training.training_orchestrator import TrainingOrchestrator
from app.training.comprehensive_training_system import TrainingConfig
from app.db.db_executor import DatabaseExecutor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_1_quick_training():
    """Example 1: Quick training with default parameters"""
    logger.info("="*50)
    logger.info("EXAMPLE 1: QUICK TRAINING")
    logger.info("="*50)
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, "./example_1_output")
        
        # Run initial training only
        best_model_path = await orchestrator.run_initial_training()
        
        logger.info(f"Quick training completed!")
        logger.info(f"Best model saved to: {best_model_path}")
        
    finally:
        db.close()


async def example_2_hyperparameter_optimization():
    """Example 2: Hyperparameter optimization"""
    logger.info("="*50)
    logger.info("EXAMPLE 2: HYPERPARAMETER OPTIMIZATION")
    logger.info("="*50)
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, "./example_2_output")
        
        # Create custom base configuration
        base_config = TrainingConfig(
            pair="SOL/USD",
            start_date=datetime(2022, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2025, 6, 1, tzinfo=timezone.utc),
            total_timesteps=50000,  # Reduced for faster optimization
            random_seeds=[42, 123]  # Fewer seeds for speed
        )
        
        # Run optimization with limited trials for demo
        best_config = await orchestrator.run_hyperparameter_optimization(
            base_config, n_trials=10
        )
        
        logger.info("Optimization completed!")
        logger.info(f"Best learning rate: {best_config.learning_rate_initial}")
        logger.info(f"Best n_steps: {best_config.n_steps}")
        logger.info(f"Best batch_size: {best_config.batch_size}")
        
    finally:
        db.close()


async def example_3_walk_forward_validation():
    """Example 3: Walk-forward validation"""
    logger.info("="*50)
    logger.info("EXAMPLE 3: WALK-FORWARD VALIDATION")
    logger.info("="*50)
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, "./example_3_output")
        
        # Create configuration for shorter validation
        config = TrainingConfig(
            pair="SOL/USD",
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2025, 1, 1, tzinfo=timezone.utc),
            total_timesteps=25000,  # Reduced for faster validation
            random_seeds=[42]  # Single seed for speed
        )
        
        # Run walk-forward validation
        analysis = await orchestrator.run_walk_forward_validation(config)
        
        logger.info("Walk-forward validation completed!")
        logger.info(f"Consistency ratio: {analysis['time_analysis']['consistency_ratio']:.2%}")
        logger.info(f"Average Sharpe ratio: {analysis['statistics']['sharpe_ratio']['mean']:.4f}")
        
    finally:
        db.close()


async def example_4_complete_pipeline():
    """Example 4: Complete training pipeline"""
    logger.info("="*50)
    logger.info("EXAMPLE 4: COMPLETE PIPELINE")
    logger.info("="*50)
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, "./example_4_output")
        
        # Run complete pipeline with limited scope for demo
        results = await orchestrator.run_complete_training_pipeline(
            pair="SOL/USD",
            optimize_hyperparams=True,  # Enable optimization
            run_validation=True         # Enable validation
        )
        
        logger.info("Complete pipeline finished!")
        logger.info(f"Status: {results['pipeline_status']}")
        
        if results['pipeline_status'] == 'completed':
            logger.info(f"Best model: {results['summary']['best_model_path']}")
            logger.info(f"Ready for live trading: {results['summary']['ready_for_live_trading']}")
        
    finally:
        db.close()


async def example_5_incremental_learning():
    """Example 5: Incremental learning simulation"""
    logger.info("="*50)
    logger.info("EXAMPLE 5: INCREMENTAL LEARNING")
    logger.info("="*50)
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, "./example_5_output")
        
        # First, train an initial model
        logger.info("Training initial model...")
        best_model_path = await orchestrator.run_initial_training()
        
        # Setup incremental learning
        logger.info("Setting up incremental learning...")
        incremental_config = await orchestrator.setup_incremental_learning(
            best_model_path, "SOL/USD"
        )
        
        # Simulate incremental update
        logger.info("Running incremental update...")
        result = await orchestrator.run_incremental_update(incremental_config)
        
        if result:
            logger.info("Incremental learning completed!")
            logger.info(f"Model updated: {result.model_updated}")
            logger.info(f"Performance improvement: {result.improvement:.4f}")
        else:
            logger.info("No incremental update was needed")
        
    finally:
        db.close()


async def example_6_custom_configuration():
    """Example 6: Custom configuration for specific requirements"""
    logger.info("="*50)
    logger.info("EXAMPLE 6: CUSTOM CONFIGURATION")
    logger.info("="*50)
    
    # Initialize database
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, "./example_6_output")
        
        # Create highly customized configuration
        custom_config = TrainingConfig(
            pair="SOL/USD",
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2025, 7, 1, tzinfo=timezone.utc),
            
            # Custom data splits
            train_ratio=0.8,
            validation_ratio=0.15,
            test_ratio=0.05,
            
            # Aggressive hyperparameters for high-frequency trading
            learning_rate_initial=0.001,
            learning_rate_final=0.0001,
            n_steps=512,
            batch_size=64,
            n_epochs=15,
            gamma=0.99,  # Higher gamma for longer-term rewards
            clip_range=0.1,  # Tighter clipping for stability
            ent_coef_initial=0.02,  # Lower entropy for more deterministic policy
            
            # Extended training
            total_timesteps=200000,
            random_seeds=[42, 123, 456, 789, 999, 111, 222],  # More seeds for robustness
            
            # Frequent evaluation
            evaluation_frequency=5000,
            save_frequency=10000
        )
        
        # Run training with custom configuration
        results = await orchestrator.training_system.run_experiment(custom_config)
        
        logger.info("Custom configuration training completed!")
        logger.info(f"Number of models trained: {len(results)}")
        
        if results:
            best_result = max(results, key=lambda r: r.metrics.sharpe_ratio)
            logger.info(f"Best Sharpe ratio: {best_result.metrics.sharpe_ratio:.4f}")
            logger.info(f"Best total return: {best_result.metrics.total_return:.2f}%")
            logger.info(f"Best max drawdown: {best_result.metrics.max_drawdown:.2f}%")
        
    finally:
        db.close()


def print_usage_guide():
    """Print usage guide for the training system"""
    print("""
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║                    COMPREHENSIVE TRAINING SYSTEM USAGE GUIDE                 ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    
    This training system provides several modes of operation:
    
    1. QUICK TRAINING (example_1_quick_training)
       - Fast training with default parameters
       - Good for initial testing and prototyping
       - Uses 5 random seeds for robustness
    
    2. HYPERPARAMETER OPTIMIZATION (example_2_hyperparameter_optimization)
       - Systematic search for optimal hyperparameters
       - Uses Optuna for efficient optimization
       - Recommended before production training
    
    3. WALK-FORWARD VALIDATION (example_3_walk_forward_validation)
       - Tests model robustness across time periods
       - Simulates real trading conditions
       - Essential for production deployment
    
    4. COMPLETE PIPELINE (example_4_complete_pipeline)
       - Runs optimization + training + validation
       - Comprehensive solution for production models
       - Provides ready-to-deploy models
    
    5. INCREMENTAL LEARNING (example_5_incremental_learning)
       - Updates models with new data
       - Maintains performance in live trading
       - Prevents model degradation over time
    
    6. CUSTOM CONFIGURATION (example_6_custom_configuration)
       - Demonstrates advanced customization
       - Tailored for specific trading requirements
       - Shows all available parameters
    
    COMMAND LINE USAGE:
    
    # Run complete pipeline
    python training_orchestrator.py --mode pipeline --pair SOL/USD
    
    # Run hyperparameter optimization only
    python training_orchestrator.py --mode optimize --trials 50
    
    # Run incremental learning
    python training_orchestrator.py --mode incremental --model-path ./models/best_model.zip
    
    ENVIRONMENT VARIABLES REQUIRED:
    - DB_NAME: Database name
    - DB_USER: Database username
    - DB_PASSWORD: Database password
    - DB_HOST: Database host
    - DB_PORT: Database port
    
    """)


async def main():
    """Main function to run examples"""
    print_usage_guide()
    
    print("Available examples:")
    print("1. Quick Training")
    print("2. Hyperparameter Optimization")
    print("3. Walk-Forward Validation")
    print("4. Complete Pipeline")
    print("5. Incremental Learning")
    print("6. Custom Configuration")
    print("0. Exit")
    
    while True:
        try:
            choice = input("\nEnter example number (0-6): ").strip()
            
            if choice == "0":
                break
            elif choice == "1":
                await example_1_quick_training()
            elif choice == "2":
                await example_2_hyperparameter_optimization()
            elif choice == "3":
                await example_3_walk_forward_validation()
            elif choice == "4":
                await example_4_complete_pipeline()
            elif choice == "5":
                await example_5_incremental_learning()
            elif choice == "6":
                await example_6_custom_configuration()
            else:
                print("Invalid choice. Please enter 0-6.")
                continue
                
            print("\nExample completed! Choose another or press 0 to exit.")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error running example: {e}")
            logger.error(f"Example failed: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
