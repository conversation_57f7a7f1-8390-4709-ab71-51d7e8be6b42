#!/usr/bin/env python3
"""
Complete PPO Trading Model Training Script

This script executes the comprehensive training system with the exact configuration
that was demonstrated in the pipeline execution, allowing you to replicate the results.

Usage:
    python run_complete_training.py

Environment Variables Required:
    DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT
"""

import asyncio
import os
import sys
import logging
from datetime import datetime, timezone

# Add project root to path
sys.path.append('.')

from app.training.training_orchestrator import TrainingOrchestrator
from app.training.comprehensive_training_system import TrainingConfig
from app.db.db_executor import DatabaseExecutor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def run_complete_training():
    """Execute the complete training pipeline with demonstrated configuration"""

    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'),
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )

    try:
        logger.info("="*60)
        logger.info("STARTING COMPLETE PPO TRAINING PIPELINE")
        logger.info("="*60)

        # Test database connection first
        try:
            result = db.execute_select(
                "SELECT COUNT(*) FROM kraken_ohlc WHERE pair = %s",
                ('SOL/USD',)
            )
            candle_count = result[0][0] if result else 0
            logger.info(f"Database connection successful - {candle_count} SOL/USD candles available")

            if candle_count < 1000:
                logger.warning(f"Only {candle_count} candles available - may not be sufficient for training")

        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            logger.error("Please check your database configuration and ensure SOL/USD data is available")
            return None

        # Create orchestrator
        orchestrator = TrainingOrchestrator(db, './training_results')

        # Configuration following proper time-series data splitting recommendations
        # Using full 4-year dataset with proper chronological splits
        config = TrainingConfig(
            pair='SOL/USD',
            # Full training period: June 2021 - December 2023 (70% of 4 years)
            start_date=datetime(2021, 6, 1, tzinfo=timezone.utc),
            end_date=datetime(2023, 12, 31, tzinfo=timezone.utc),

            # PPO hyperparameters (demonstrated optimal values)
            learning_rate_initial=0.0003,
            learning_rate_final=0.00005,
            n_steps=1024,
            batch_size=128,
            n_epochs=10,  # Your current optimal setting
            gamma=0.97,
            gae_lambda=0.95,
            clip_range=0.2,
            ent_coef_initial=0.05,
            ent_coef_final=0.01,
            vf_coef=0.5,
            max_grad_norm=0.5,

            # Training parameters (increased for larger dataset)
            total_timesteps=50000,  # Increased for 2.5 years of data
            random_seeds=[42, 123, 456],  # Multiple seeds for robustness
            evaluation_frequency=10000,
            save_frequency=25000,

            # Data splits (chronological within training period)
            train_ratio=0.8,   # 80% of training period for actual training
            validation_ratio=0.15,  # 15% for validation
            test_ratio=0.05    # 5% for testing within training period
        )

        logger.info("Training Configuration:")
        logger.info(f"  Pair: {config.pair}")
        logger.info(f"  Date Range: {config.start_date.date()} to {config.end_date.date()}")
        logger.info(f"  N Epochs: {config.n_epochs}")
        logger.info(f"  Learning Rate: {config.learning_rate_initial} → {config.learning_rate_final}")
        logger.info(f"  Batch Size: {config.batch_size}")
        logger.info(f"  N Steps: {config.n_steps}")
        logger.info(f"  Total Timesteps: {config.total_timesteps}")
        logger.info(f"  Random Seeds: {config.random_seeds}")

        logger.info("\nStarting training with demonstrated configuration...")

        # Execute training
        start_time = datetime.now()
        results = await orchestrator.training_system.run_experiment(config)
        end_time = datetime.now()

        if results and len(results) > 0:
            logger.info("="*60)
            logger.info("TRAINING COMPLETED SUCCESSFULLY")
            logger.info("="*60)

            # Find best result
            best_result = max(results, key=lambda x: x.metrics.sharpe_ratio)

            logger.info("RESULTS SUMMARY:")
            logger.info(f"  Training Duration: {end_time - start_time}")
            logger.info(f"  Models Trained: {len(results)}")
            logger.info("")
            logger.info("BEST MODEL PERFORMANCE:")
            logger.info(f"  Seed: {best_result.seed}")
            logger.info(f"  Sharpe Ratio: {best_result.metrics.sharpe_ratio:.4f}")
            logger.info(f"  Total Return: {best_result.metrics.total_return:.2f}%")
            logger.info(f"  Final Net Worth: ${best_result.metrics.final_net_worth:.2f}")
            logger.info(f"  Maximum Drawdown: {best_result.metrics.max_drawdown:.2f}%")
            logger.info(f"  Model Path: {best_result.model_path}")

            # Performance comparison with demonstrated results
            logger.info("")
            logger.info("PERFORMANCE COMPARISON:")
            target_sharpe = 1.266
            target_return = 4.75
            target_net_worth = 5237.49

            sharpe_match = "✅" if best_result.metrics.sharpe_ratio >= target_sharpe * 0.8 else "❌"
            return_match = "✅" if best_result.metrics.total_return >= target_return * 0.8 else "❌"
            net_worth_match = "✅" if best_result.metrics.final_net_worth >= target_net_worth * 0.98 else "❌"

            logger.info(f"  Sharpe Ratio: {best_result.metrics.sharpe_ratio:.4f} vs {target_sharpe:.4f} {sharpe_match}")
            logger.info(f"  Total Return: {best_result.metrics.total_return:.2f}% vs {target_return:.2f}% {return_match}")
            logger.info(f"  Net Worth: ${best_result.metrics.final_net_worth:.2f} vs ${target_net_worth:.2f} {net_worth_match}")

            # All results summary
            logger.info("")
            logger.info("ALL SEEDS PERFORMANCE:")
            for i, result in enumerate(results):
                logger.info(f"  Seed {result.seed}: Sharpe {result.metrics.sharpe_ratio:.4f}, "
                          f"Return {result.metrics.total_return:.2f}%, "
                          f"Net Worth ${result.metrics.final_net_worth:.2f}")

            # Success assessment
            successful_seeds = sum(1 for r in results if r.metrics.sharpe_ratio > 1.0)
            logger.info("")
            logger.info("SUCCESS ASSESSMENT:")
            logger.info(f"  Seeds with Sharpe > 1.0: {successful_seeds}/{len(results)}")

            if successful_seeds > 0:
                logger.info("🎉 TRAINING SUCCESSFUL - Model ready for deployment!")
            else:
                logger.info("⚠️  TRAINING NEEDS IMPROVEMENT - Consider adjusting parameters")

            return results

        else:
            logger.error("❌ TRAINING FAILED - No results obtained")
            logger.error("Please check:")
            logger.error("  - Database connection and data availability")
            logger.error("  - Environment variables are set correctly")
            logger.error("  - Sufficient disk space for models and logs")
            return None

    except Exception as e:
        logger.error(f"Training pipeline failed: {e}")
        logger.error("Full error details:", exc_info=True)
        return None

    finally:
        db.close()


async def verify_environment():
    """Verify that the environment is properly set up"""
    logger.info("Verifying environment setup...")

    # Check required environment variables
    required_vars = ['DB_NAME', 'DB_USER', 'DB_PASSWORD', 'DB_HOST', 'DB_PORT']
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        logger.error(f"Missing environment variables: {missing_vars}")
        logger.error("Please set these variables before running the training")
        return False

    # Check required directories
    required_dirs = ['models', 'training_results']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name, exist_ok=True)
            logger.info(f"Created directory: {dir_name}")

    # Test imports
    try:
        import stable_baselines3
        import optuna
        import torch
        import gymnasium
        logger.info("✅ All required packages are available")
    except ImportError as e:
        logger.error(f"❌ Missing required package: {e}")
        return False

    logger.info("✅ Environment verification completed")
    return True


async def main():
    """Main execution function"""
    logger.info("🚀 PPO Trading Model Training Script")
    logger.info("="*60)

    # Verify environment
    if not await verify_environment():
        logger.error("Environment verification failed. Please fix the issues and try again.")
        return

    # Run training
    results = await run_complete_training()

    if results:
        logger.info("\n🎯 TRAINING COMPLETED SUCCESSFULLY!")
        logger.info("Next steps:")
        logger.info("  1. Review the model performance metrics")
        logger.info("  2. Test the model with paper trading")
        logger.info("  3. Deploy to live trading when ready")
    else:
        logger.error("\n❌ TRAINING FAILED!")
        logger.error("Please check the logs above for error details")


if __name__ == "__main__":
    asyncio.run(main())
