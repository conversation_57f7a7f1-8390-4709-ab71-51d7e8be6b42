import logging
import numpy as np
import pandas as pd
import os
import re
import asyncio
import threading
import shutil
import json
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
from stable_baselines3 import PPO
from .base_strategy import BaseStrategy
from app.utils.indicator_calculator import IndicatorCalculator
from app.trade_manager.portfolio_manager import PortfolioManager
from app.trade_manager.trade_manager import TradeManager
from .trading_env import TradingEnv

# Import comprehensive training components
from app.training.comprehensive_training_system import ComprehensiveTrainingSystem, TrainingConfig
from app.training.incremental_learning import IncrementalLearningSystem, IncrementalConfig, UpdateResult
from app.training.walk_forward_validation import WalkForwardValidator, WalkForwardConfig
from app.db.db_executor import DatabaseExecutor

logger = logging.getLogger(__name__)


class PPOStrategy(BaseStrategy):
    """RL-based strategy using Proximal Policy Optimization (PPO)"""

    def __init__(self, pair: str,
                 train_on_startup: bool = False,  # Whether to train on initialization
                 short_window: int = 7,
                 long_window: int = 21,
                 take_profit_levels: list = [0.05, 0.1, 0.15],
                 stop_loss: float = -0.2,
                 interval: str = '1h',
                 max_investment: float = 7500,
                 taker_maker_fee: float = 0.0026,
                 min_investment: float = 250,
                 position_size_multiplier: float = 20.0,
                 volatility_multiplier: float = 3.0):

        super().__init__(
            pair=pair,
            take_profit_levels=take_profit_levels,
            stop_loss=stop_loss,
            interval=interval,
            max_investment=max_investment,
            taker_maker_fee=taker_maker_fee,
            min_investment=min_investment,
            position_size_multiplier=position_size_multiplier,
            volatility_multiplier=volatility_multiplier
        )

        self.short_window = short_window
        self.long_window = long_window
        self.model_path = self.find_newest_model()
        self.train_on_startup = train_on_startup
        self.model = None
        self.env = None
        self.portfolio_manager = None
        self.trade_manager = None
        self.historical_data = pd.DataFrame()
        self.last_action = 0  # 0 = hold, 1 = buy, 2 = sell
        self.retrain_interval_days = 7  # Days between retraining
        # Last time the model was trained
        self.last_training_time: Optional[datetime] = None

        # Comprehensive training configuration
        self.training_config = {
            'incremental_learning_enabled': True,
            'training_window_months': 18,
            'validation_window_months': 3,
            'performance_threshold': 0.1,  # 10% improvement required
            'max_updates_per_session': 3,
            'learning_rate_decay': 0.8,
            'backup_models': True,
            'thread_safe_updates': True,
            'success_criteria': {
                'min_sharpe_ratio': 1.0,
                'min_total_return': 3.0,  # 3% minimum return
                'max_drawdown': 15.0,     # 15% maximum drawdown
                'min_consistency_ratio': 0.6  # 60% consistency
            }
        }

        # Training system components (initialized lazily)
        self._db_connection: Optional[DatabaseExecutor] = None
        self._training_system: Optional[ComprehensiveTrainingSystem] = None
        self._incremental_system: Optional[IncrementalLearningSystem] = None
        self._model_backup_dir = Path("./model_backups")
        self._model_backup_dir.mkdir(exist_ok=True)

        # Thread safety for model updates
        self._model_update_lock = threading.Lock()
        self._training_in_progress = False

        # Initialize model if path is provided
        if self.model_path:
            try:
                self.model = PPO.load(self.model_path)
                logger.info(f"Loaded PPO model from {self.model_path}")
            except Exception as e:
                logger.error(f"Failed to load model: {e}")

    def find_newest_model(self, models_folder="models"):
        pattern = re.compile(r"ppo_SOLUSD_(\d{8}_\d{6})\.zip")
        newest_model = None
        newest_timestamp = None

        for filename in os.listdir(models_folder):
            match = pattern.match(filename)
            if match:
                timestamp_str = match.group(1)
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                if newest_timestamp is None or timestamp > newest_timestamp:
                    newest_timestamp = timestamp
                    newest_model = filename

        if newest_model:
            return os.path.join(models_folder, newest_model)
        else:
            return None  # No matching models found

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepares data before running the strategy"""
        logger.info("Preprocessing data for PPO strategy")
        cols_to_convert = ['open_price', 'high_price',
                           'low_price', 'close_price', 'volume']
        df[cols_to_convert] = df[cols_to_convert].apply(
            pd.to_numeric, errors='coerce')
        # df = self.resample_data(df)

        # --- Calculate indicators ---
        # Trend
        df["EMA_short"] = IndicatorCalculator.calculate_ema(df, 7)
        df["EMA_long"] = IndicatorCalculator.calculate_ema(df, 21)

        # New Trend Indicators
        df["macd"], df["macd_signal"], df["macd_hist"] = IndicatorCalculator.calculate_macd(
            df)
        df["sar"] = IndicatorCalculator.calculate_parabolic_sar(df)

        # Momentum
        df["rsi"] = IndicatorCalculator.calculate_rsi(df)
        df["momentum"] = IndicatorCalculator.calculate_momentum(df, 10)
        df["stoch_k"], df["stoch_d"] = IndicatorCalculator.calculate_stochastic(
            df)
        df["cci"] = IndicatorCalculator.calculate_commodity_channel_index(df)
        df["roc"] = IndicatorCalculator.calculate_rate_of_change(df)

        # Volatility
        df["atr"] = IndicatorCalculator.calculate_atr(df)
        df["adx"] = IndicatorCalculator.calculate_average_directional_index(df)
        df["bb_upper"], df["bb_middle"], df["bb_lower"] = IndicatorCalculator.calculate_bollinger_bands(
            df)
        df["bb_percent_b"] = IndicatorCalculator.calculate_bollinger_percent_b(
            df)
        df["volatility_ratio"] = IndicatorCalculator.calculate_volatility_ratio(
            df)

        # Volume
        df["vwap"] = IndicatorCalculator.calculate_vwap(df)
        df["obv"] = IndicatorCalculator.calculate_on_balance_volume(df)
        df["relative_volume"] = IndicatorCalculator.calculate_relative_volume(
            df)
        df["volume_roc"] = IndicatorCalculator.calculate_volume_roc(df)
        df["stddev_returns"] = IndicatorCalculator.calculate_stddev_of_returns(
            df)
        df["mfi"] = IndicatorCalculator.calculate_money_flow_index(df)
        df["ad_line"] = IndicatorCalculator.calculate_chaikin_ad_line(df)
        df["adosc"] = IndicatorCalculator.calculate_chaikin_oscillator(df)
        df['vwap_slope'] = IndicatorCalculator.calculate_vwap_slope(df)
        df['obv_slope'] = IndicatorCalculator.calculate_obv_slope(df)

        # Volatility Bands
        df["kc_upper"], df["kc_middle"], df["kc_lower"] = IndicatorCalculator.calculate_keltner_channels(
            df)

        # Market Regime
        df["market_regime"] = IndicatorCalculator.detect_market_regime(df)

        # Candlestick Patterns
        df = IndicatorCalculator.detect_candlestick_patterns(df)

        # Temporal alignment for order book features: apply exponential weighted average with 1 hour span
        ob_feature_names = [
            'ob_spread', 'ob_spread_pct', 'ob_mid_price', 'ob_volume_imbalance',
            'ob_bid_depth_5', 'ob_ask_depth_5', 'ob_total_bid_vol', 'ob_total_ask_vol',
            'ob_bid_impact', 'ob_ask_impact', 'ob_weighted_bid', 'ob_weighted_ask'
        ]
        for feature_name in ob_feature_names:
            if feature_name in df.columns:
                # Apply exponential weighted average with 1 hour span (assuming data frequency is minutes)
                df[feature_name] = df[feature_name].ewm(span=60, adjust=False).mean()

        # Normalize order book features if present
        # Calculate adaptive window size based on data length
        adaptive_window = min(48*60, max(20, len(df)//4))  # Between 20 and 2880, max 1/4 of data

        for feature_name in ob_feature_names:
            if feature_name in df.columns:
                # Use robust normalization for volume-related features, zscore for others
                if feature_name in ['ob_bid_depth_5', 'ob_ask_depth_5', 'ob_total_bid_vol', 'ob_total_ask_vol']:
                    df[f'{feature_name}_norm'] = self._normalize_feature(
                        df[feature_name], method='robust', window=adaptive_window, clip=5.0)
                else:
                    df[f'{feature_name}_norm'] = self._normalize_feature(
                        df[feature_name], method='zscore', window=adaptive_window, clip=5.0)

        # Add order book momentum features (momentum of ob_spread and ob_volume_imbalance)
        if 'ob_spread' in df.columns:
            df['ob_spread_momentum'] = df['ob_spread'].diff(1)
            df['ob_spread_momentum_norm'] = self._normalize_feature(
                df['ob_spread_momentum'], method='zscore', window=adaptive_window, clip=5.0)
        if 'ob_volume_imbalance' in df.columns:
            df['ob_volume_imbalance_momentum'] = df['ob_volume_imbalance'].diff(1)
            df['ob_volume_imbalance_momentum_norm'] = self._normalize_feature(
                df['ob_volume_imbalance_momentum'], method='zscore', window=adaptive_window, clip=5.0)

        # Use selective dropna to preserve data integrity
        # Only drop rows where critical price data is missing
        critical_columns = ['open_price', 'high_price', 'low_price', 'close_price', 'volume']
        available_critical_columns = [col for col in critical_columns if col in df.columns]

        if available_critical_columns:
            df = df.dropna(subset=available_critical_columns)
            logger.info(f"Dropped rows with missing critical price data. Remaining rows: {len(df)}")
        else:
            logger.warning("No critical price columns found, skipping dropna operation")

        # Fill remaining NaN values in technical indicators and orderbook features with forward fill then 0
        df = df.ffill().fillna(0)
        # Store processed data
        self.historical_data = df.copy()

        # Train the model if specified and sufficient data
        if self.train_on_startup and len(df) > 1000 and self.model is None:
            self._train_model(df)

        return df

    def _default_order_book_features(self):
        return {
            'ob_spread': 0.0,
            'ob_spread_pct': 0.0,
            'ob_mid_price': 0.0,
            'ob_volume_imbalance': 0.0,
            'ob_bid_depth_5': 0.0,
            'ob_ask_depth_5': 0.0,
            'ob_total_bid_vol': 0.0,
            'ob_total_ask_vol': 0.0,
            'ob_bid_impact': 0.0,
            'ob_ask_impact': 0.0,
            'ob_weighted_bid': 0.0,
            'ob_weighted_ask': 0.0
        }

    def calculate_orderbook_features(self, orderbook_data: dict) -> dict:
        """
        Calculate order book features from orderbook_data dict.

        orderbook_data format:
        {
            'pair': str,
            'snapshot_time': datetime,
            'bids': [{'price': float, 'qty': float}, ...],
            'asks': [{'price': float, 'qty': float}, ...],
            'checksum': int
        }
        """
        bids = orderbook_data.get('bids', [])
        asks = orderbook_data.get('asks', [])

        # Defensive: if bids or asks are empty, return defaults
        if not bids or not asks:
            return self._default_order_book_features()

        # Validate and clean bid/ask data
        try:
            # Filter out invalid entries and convert to float
            valid_bids = []
            for bid in bids:
                if isinstance(bid, dict) and 'price' in bid and 'qty' in bid:
                    price = float(bid['price'])
                    qty = float(bid['qty'])
                    if price > 0 and qty > 0:  # Only include positive prices and quantities
                        valid_bids.append({'price': price, 'qty': qty})

            valid_asks = []
            for ask in asks:
                if isinstance(ask, dict) and 'price' in ask and 'qty' in ask:
                    price = float(ask['price'])
                    qty = float(ask['qty'])
                    if price > 0 and qty > 0:  # Only include positive prices and quantities
                        valid_asks.append({'price': price, 'qty': qty})

            # Check if we have valid data after filtering
            if not valid_bids or not valid_asks:
                return self._default_order_book_features()

            # Sort bids descending by price, asks ascending by price
            bids_sorted = sorted(valid_bids, key=lambda x: x['price'], reverse=True)
            asks_sorted = sorted(valid_asks, key=lambda x: x['price'])

            best_bid = bids_sorted[0]['price']
            best_ask = asks_sorted[0]['price']

            # Validate market integrity - spread should be non-negative
            if best_ask < best_bid:
                # Crossed market detected - this shouldn't happen in normal conditions
                # Log warning and return defaults
                logger.warning(f"Crossed market detected: best_bid={best_bid}, best_ask={best_ask}")
                logger.warning(f"Sample bids: {bids_sorted[:3]}")
                logger.warning(f"Sample asks: {asks_sorted[:3]}")
                return self._default_order_book_features()

            ob_spread = best_ask - best_bid
            ob_spread_pct = (ob_spread / best_bid * 100) if best_bid > 0 else 0.0
            ob_mid_price = (best_ask + best_bid) / 2

            # Additional validation for debugging
            if ob_spread < 0:
                logger.error(f"Negative spread calculated: {ob_spread}, best_bid={best_bid}, best_ask={best_ask}")
                return self._default_order_book_features()

        except (ValueError, TypeError, KeyError) as e:
            logger.warning(f"Error processing order book data: {e}")
            return self._default_order_book_features()

        try:
            bid_volumes = [level['qty'] for level in bids_sorted]
            ask_volumes = [level['qty'] for level in asks_sorted]

            total_bid_vol = sum(bid_volumes)
            total_ask_vol = sum(ask_volumes)

            ob_volume_imbalance = (total_bid_vol - total_ask_vol) / \
                (total_bid_vol + total_ask_vol) if (total_bid_vol + total_ask_vol) > 0 else 0.0

            ob_bid_depth_5 = sum(bid_volumes[:5])
            ob_ask_depth_5 = sum(ask_volumes[:5])

            # VWAP calculations for top 5 bids and asks
            def vwap(levels):
                if not levels:
                    return 0.0
                total_vol = sum(level['qty'] for level in levels)
                if total_vol == 0:
                    return 0.0
                return sum(level['price'] * level['qty'] for level in levels) / total_vol

            ob_weighted_bid = vwap(bids_sorted[:5])
            ob_weighted_ask = vwap(asks_sorted[:5])

            # Price impact calculations for buying/selling 1000 units
            def price_impact(levels, qty_to_trade):
                if not levels or qty_to_trade <= 0:
                    return 0.0
                remaining_qty = qty_to_trade
                impact_price = 0.0
                for level in levels:
                    level_qty = level['qty']
                    trade_qty = min(remaining_qty, level_qty)
                    impact_price += trade_qty * level['price']
                    remaining_qty -= trade_qty
                    if remaining_qty <= 0:
                        break
                return (impact_price / qty_to_trade) - levels[0]['price']

            ob_bid_impact = price_impact(bids_sorted, 1000)
            ob_ask_impact = price_impact(asks_sorted, 1000)

            return {
                'ob_spread': ob_spread,
                'ob_spread_pct': ob_spread_pct,
                'ob_mid_price': ob_mid_price,
                'ob_volume_imbalance': ob_volume_imbalance,
                'ob_bid_depth_5': ob_bid_depth_5,
                'ob_ask_depth_5': ob_ask_depth_5,
                'ob_total_bid_vol': total_bid_vol,
                'ob_total_ask_vol': total_ask_vol,
                'ob_bid_impact': ob_bid_impact,
                'ob_ask_impact': ob_ask_impact,
                'ob_weighted_bid': ob_weighted_bid,
                'ob_weighted_ask': ob_weighted_ask
            }

        except Exception as e:
            logger.warning(f"Error calculating order book features: {e}")
            return self._default_order_book_features()

    def _normalize_feature(self, series: pd.Series, method: str = 'zscore', window: int = 20, clip: float = 5.0) -> pd.Series:
        """
        Normalize a feature for PPO-based RL agent.

        Parameters:
        - series: the feature to normalize
        - method: 'zscore', 'minmax', or 'robust'
        - window: rolling window size
        - clip: max absolute value to clip normalized output

        Returns:
        - Normalized and clipped pd.Series
        """
        if method == 'zscore':
            rolling_mean = series.rolling(window=window).mean()
            rolling_std = series.rolling(window=window).std()

            normalized = (series - rolling_mean) / rolling_std
        elif method == 'minmax':
            rolling_min = series.rolling(window=window).min()
            rolling_max = series.rolling(window=window).max()

            normalized = (series - rolling_min) / \
                (rolling_max - rolling_min + 1e-8)
        elif method == 'robust':
            rolling_median = series.rolling(window=window).median()
            # Calculate MAD (Median Absolute Deviation) manually since mad() is deprecated
            rolling_mad = series.rolling(window=window).apply(
                lambda x: np.median(np.abs(x - np.median(x))), raw=False
            )

            normalized = (series - rolling_median) / (rolling_mad + 1e-8)
        else:
            raise ValueError(f"Unsupported normalization method: {method}")

        # Fill NaNs and clip outliers
        normalized = normalized.fillna(0.0)
        normalized = normalized.clip(lower=-clip, upper=clip)

        return normalized

    @staticmethod
    def has_valid_orderbook_data(candle: pd.Series) -> bool:
        """
        Check if the candle contains valid order book data.

        Args:
            candle: Market data candle

        Returns:
            bool: True if order book data is present and valid
        """
        required_features = [
            'ob_spread_pct', 'ob_bid_depth_5', 'ob_volume_imbalance',
            'ob_total_bid_vol', 'ob_total_ask_vol'
        ]

        missing_features = []
        invalid_features = []

        for feature in required_features:
            value = candle.get(feature, None)
            if value is None:
                missing_features.append(feature)
            elif value == 0.0:
                invalid_features.append(f"{feature}=0.0")

        # Additional validation: spread should be positive
        ob_spread_pct = candle.get('ob_spread_pct', 0.0)
        if ob_spread_pct <= 0:
            invalid_features.append(f"ob_spread_pct={ob_spread_pct}")

        # Log detailed information about missing/invalid data
        if missing_features or invalid_features:
            logger.debug(f"Order book data validation failed - Missing: {missing_features}, Invalid: {invalid_features}")
            return False

        return True

    @staticmethod
    def get_orderbook_data_status(candle: pd.Series) -> dict:
        """
        Get detailed status of order book data availability and quality.

        Args:
            candle: Market data candle

        Returns:
            dict: Status information about order book data
        """
        required_features = [
            'ob_spread_pct', 'ob_bid_depth_5', 'ob_volume_imbalance',
            'ob_total_bid_vol', 'ob_total_ask_vol', 'ob_mid_price',
            'ob_weighted_bid', 'ob_weighted_ask'
        ]

        status = {
            'has_valid_data': True,
            'missing_features': [],
            'zero_features': [],
            'present_features': [],
            'data_quality_score': 0.0
        }

        for feature in required_features:
            value = candle.get(feature, None)
            if value is None:
                status['missing_features'].append(feature)
                status['has_valid_data'] = False
            elif value == 0.0:
                status['zero_features'].append(feature)
            else:
                status['present_features'].append(feature)

        # Calculate data quality score (0.0 to 1.0)
        total_features = len(required_features)
        valid_features = len(status['present_features'])
        status['data_quality_score'] = valid_features / total_features if total_features > 0 else 0.0

        return status

    @staticmethod
    def _apply_entry_filters(candle: pd.Series, predicted_action: int) -> bool:
        """
        Apply order book filtering logic for trade entry decisions.
        This method contains the same logic used in should_enter_trade().

        Args:
            candle: Market data candle
            predicted_action: Raw model prediction (0=hold, 1=buy, 2=sell)

        Returns:
            bool: True if trade should be executed, False if filtered out
        """
        # Only apply filters for buy signals
        if predicted_action != 1:
            return predicted_action != 0  # Allow sell signals, block hold signals

        # Check if order book data is available
        if not PPOStrategy.has_valid_orderbook_data(candle):
            ob_status = PPOStrategy.get_orderbook_data_status(candle)
            logger.debug(f"Order book data insufficient for entry filtering - Quality: {ob_status['data_quality_score']:.2f}, "
                       f"Missing: {len(ob_status['missing_features'])}, Zero: {len(ob_status['zero_features'])}")
            logger.debug(f"Bypassing entry filters due to insufficient order book data")
            return True  # Allow trade when no order book data (graceful degradation)

        # Extract order book features
        ob_spread_pct = candle.get('ob_spread_pct', 0.0)
        ob_bid_depth_5 = candle.get('ob_bid_depth_5', 0.0)
        ob_volume_imbalance = candle.get('ob_volume_imbalance', 0.0)
        market_regime = candle.get('market_regime', 'neutral')

        # Time of day filter: avoid trading during low liquidity hours (0-6 UTC)
        if 'timestamp' in candle:
            hour = candle['timestamp'].hour
            if hour >= 0 and hour < 6:
                logger.debug(f"Blocking trade during low liquidity hours: {hour}:00 UTC")
                return False

        # Dynamic thresholds based on market regime
        spread_threshold = 0.5 if market_regime == 'neutral' else 0.7
        bid_depth_threshold = 100 if market_regime != 'volatile' else 150
        volume_imbalance_threshold = -0.3 if market_regime == 'neutral' else -0.2

        # Apply filters
        if ob_spread_pct > spread_threshold:
            logger.debug(f"Blocking trade: spread too wide ({ob_spread_pct:.3f} > {spread_threshold})")
            return False

        if ob_bid_depth_5 < bid_depth_threshold:
            logger.debug(f"Blocking trade: insufficient bid depth ({ob_bid_depth_5} < {bid_depth_threshold})")
            return False

        if ob_volume_imbalance < volume_imbalance_threshold:
            logger.debug(f"Blocking trade: poor volume imbalance ({ob_volume_imbalance:.3f} < {volume_imbalance_threshold})")
            return False

        return True

    @staticmethod
    def _apply_exit_filters(candle: pd.Series, predicted_action: int) -> bool:
        """
        Apply order book filtering logic for trade exit decisions.
        This method contains the same logic used in should_exit_trade().

        Args:
            candle: Market data candle
            predicted_action: Raw model prediction (0=hold, 1=buy, 2=sell)

        Returns:
            bool: True if trade should be exited, False if should hold
        """
        # Only apply filters for sell signals
        if predicted_action != 2:
            return False  # Don't exit on hold or buy signals

        # Check if order book data is available
        if not PPOStrategy.has_valid_orderbook_data(candle):
            ob_status = PPOStrategy.get_orderbook_data_status(candle)
            logger.debug(f"Order book data insufficient for exit filtering - Quality: {ob_status['data_quality_score']:.2f}, "
                       f"Missing: {len(ob_status['missing_features'])}, Zero: {len(ob_status['zero_features'])}")
            logger.debug(f"Bypassing exit filters due to insufficient order book data")
            return True  # Allow exit when no order book data (graceful degradation)

        # Extract order book features
        ob_total_bid_vol = candle.get('ob_total_bid_vol', 0.0)
        ob_total_ask_vol = candle.get('ob_total_ask_vol', 0.0)
        ob_spread_pct = candle.get('ob_spread_pct', 0.0)
        market_regime = candle.get('market_regime', 'neutral')

        # Dynamic thresholds based on market regime
        volume_threshold = 50 if market_regime == 'neutral' else 75
        spread_exit_threshold = 1.0 if market_regime == 'neutral' else 1.2

        # Apply exit filters (these are conditions that FORCE exit)
        total_volume = ob_total_bid_vol + ob_total_ask_vol
        if total_volume < volume_threshold:
            logger.debug(f"Forcing exit: low total volume ({total_volume} < {volume_threshold})")
            return True

        if ob_spread_pct > spread_exit_threshold:
            logger.debug(f"Forcing exit: spread too wide ({ob_spread_pct:.3f} > {spread_exit_threshold})")
            return True

        # If no forced exit conditions, follow model prediction
        return True

    @staticmethod
    def extract_action_from_prediction(action_array) -> int:
        """
        Safely extract action integer from model prediction output.
        Handles both scalar (0-dimensional) and array cases from stable_baselines3 PPO.

        Args:
            action_array (int, float, np.ndarray, list): Output from model.predict() - can be scalar or array

        Returns:
            int: Action value as integer
        """
        try:
            # Try to access as array first
            if hasattr(action_array, 'ndim') and action_array.ndim == 0:
                # 0-dimensional numpy array
                return int(action_array.item())
            elif hasattr(action_array, '__len__') and len(action_array) > 0:
                # Multi-dimensional array
                return int(action_array[0])
            else:
                # Scalar value
                return int(action_array)
        except (IndexError, TypeError):
            # Fallback: treat as scalar
            return int(action_array)

    def linear_schedule(self, initial_value, final_value):
        """Returns a function that computes a linear schedule."""
        def schedule(progress_remaining):
            return initial_value + (final_value - initial_value) * (1 - progress_remaining)
        return schedule

    def _train_model(self, df: pd.DataFrame, timesteps: int = 100000,
                    incremental: bool = True, force_retrain: bool = False) -> bool:
        """
        Enhanced PPO model training with comprehensive validation and incremental learning

        Args:
            df: Historical data for training
            timesteps: Number of training timesteps
            incremental: Whether to use incremental learning (fine-tuning)
            force_retrain: Force retraining even if not needed

        Returns:
            bool: True if training was successful and model was updated
        """
        logger.info("="*80)
        logger.info("COMPREHENSIVE PPO MODEL TRAINING")
        logger.info("="*80)
        logger.info(f"Training mode: {'Incremental' if incremental else 'Full'}")
        logger.info(f"Data points: {len(df)}")
        logger.info(f"Timesteps: {timesteps}")

        # Thread safety check
        if self._training_in_progress:
            logger.warning("Training already in progress, skipping")
            return False

        with self._model_update_lock:
            self._training_in_progress = True

            try:
                # Initialize training systems if needed
                if not self._initialize_training_systems():
                    logger.error("Failed to initialize training systems")
                    return False

                # Check if training is needed
                if not force_retrain and not self._should_retrain():
                    logger.info("Training not needed at this time")
                    return False

                # Backup current model
                if self.model and self.training_config['backup_models']:
                    self._backup_current_model()

                # Determine training approach
                if incremental and self.model and self.training_config['incremental_learning_enabled']:
                    success = self._run_incremental_training(df, timesteps)
                else:
                    success = self._run_comprehensive_training(df, timesteps)

                if success:
                    self.last_training_time = datetime.now()
                    logger.info("✅ Model training completed successfully")
                    return True
                else:
                    logger.error("❌ Model training failed")
                    # Restore backup if available
                    if self.training_config['backup_models']:
                        self._restore_backup_model()
                    return False

            except Exception as e:
                logger.error(f"Error in comprehensive training: {e}")
                logger.error("Full error details:", exc_info=True)

                # Restore backup on error
                if self.training_config['backup_models']:
                    self._restore_backup_model()
                return False

            finally:
                self._training_in_progress = False

    def _initialize_training_systems(self) -> bool:
        """Initialize comprehensive training systems"""
        try:
            if not self._db_connection:
                # Initialize database connection
                self._db_connection = DatabaseExecutor(
                    db_name=os.getenv('DB_NAME', 'trading_db'),
                    db_user=os.getenv('DB_USER', 'postgres'),
                    db_password=os.getenv('DB_PASSWORD', 'password'),
                    db_host=os.getenv('DB_HOST', 'localhost'),
                    db_port=int(os.getenv('DB_PORT', '5432'))
                )

            if not self._training_system:
                self._training_system = ComprehensiveTrainingSystem(
                    self._db_connection,
                    f"./training_output/{self.pair.replace('/', '')}"
                )

            if not self._incremental_system:
                self._incremental_system = IncrementalLearningSystem(
                    self._db_connection,
                    f"./incremental_models/{self.pair.replace('/', '')}"
                )

            return True

        except Exception as e:
            logger.error(f"Failed to initialize training systems: {e}")
            return False

    def _should_retrain(self) -> bool:
        """Determine if model retraining is needed"""
        # Check if enough time has passed
        if self.last_training_time:
            days_since_training = (datetime.now() - self.last_training_time).days
            if days_since_training < self.retrain_interval_days:
                logger.info(f"Only {days_since_training} days since last training, minimum is {self.retrain_interval_days}")
                return False

        # Check if we have sufficient new data
        # This could be enhanced to check for actual new market data
        return True

    def _backup_current_model(self) -> bool:
        """Backup the current model before training"""
        try:
            if not self.model_path or not os.path.exists(self.model_path):
                logger.warning("No current model to backup")
                return False

            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{self.pair.replace('/', '')}_{timestamp}.zip"
            backup_path = self._model_backup_dir / backup_filename

            shutil.copy2(self.model_path, backup_path)
            logger.info(f"Model backed up to {backup_path}")
            return True

        except Exception as e:
            logger.error(f"Failed to backup model: {e}")
            return False

    def _restore_backup_model(self) -> bool:
        """Restore the most recent backup model"""
        try:
            # Find most recent backup
            backup_files = list(self._model_backup_dir.glob(f"backup_{self.pair.replace('/', '')}*.zip"))
            if not backup_files:
                logger.warning("No backup models found")
                return False

            # Sort by modification time and get the most recent
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)

            # Restore the backup
            if self.model_path:
                shutil.copy2(latest_backup, self.model_path)
                # Reload the model
                self.model = PPO.load(self.model_path)
                logger.info(f"Model restored from backup: {latest_backup}")
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to restore backup model: {e}")
            return False

    def _run_incremental_training(self, df: pd.DataFrame, timesteps: int) -> bool:
        """Run incremental learning with the existing model"""
        logger.info("Starting incremental training (fine-tuning existing model)")

        try:
            # Validate we have a model to work with
            if not self.model_path or not os.path.exists(self.model_path):
                logger.error("No existing model found for incremental training")
                return False

            # Create incremental learning configuration
            config = IncrementalConfig(
                pair=self.pair,
                model_path=self.model_path,
                update_frequency_days=self.retrain_interval_days,
                training_window_months=self.training_config['training_window_months'],
                validation_window_months=self.training_config['validation_window_months'],
                performance_threshold=self.training_config['performance_threshold'],
                learning_rate_decay=self.training_config['learning_rate_decay'],
                max_updates_per_session=self.training_config['max_updates_per_session']
            )

            # Run incremental learning cycle
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                if self._incremental_system is None:
                    logger.error("Incremental system not initialized")
                    return False

                result = loop.run_until_complete(
                    self._incremental_system.run_incremental_learning_cycle(config)
                )

                if result and result.model_updated and result.new_model_path:
                    # Update our model with the new one
                    self.model = PPO.load(result.new_model_path)
                    self.model_path = result.new_model_path

                    logger.info(f"✅ Incremental training successful")
                    logger.info(f"   Performance improvement: {result.improvement:.4f}")
                    logger.info(f"   New model path: {result.new_model_path}")

                    # Validate the updated model
                    if self._validate_model_performance(result):
                        return True
                    else:
                        logger.warning("Model validation failed, keeping previous model")
                        return False
                else:
                    logger.info("No model update needed (insufficient improvement)")
                    return True  # Not an error, just no update needed

            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Incremental training failed: {e}")
            logger.error("Full error details:", exc_info=True)
            return False

    def _run_comprehensive_training(self, df: pd.DataFrame, timesteps: int) -> bool:
        """Run comprehensive training from scratch"""
        logger.info("Starting comprehensive training (full model training)")

        try:
            # Prepare data with proper time-series splits
            train_df, val_df, _ = self._prepare_training_data(df)  # test_df not used in this context

            if train_df.empty:
                logger.error("No training data available")
                return False

            # Create training configuration
            config = TrainingConfig(
                pair=self.pair,
                start_date=train_df['timestamp'].min(),
                end_date=train_df['timestamp'].max(),

                # PPO hyperparameters (optimized values)
                learning_rate_initial=0.0003,
                learning_rate_final=0.00005,
                n_steps=1024,
                batch_size=128,
                n_epochs=10,
                gamma=0.97,
                gae_lambda=0.95,
                clip_range=0.2,
                ent_coef_initial=0.05,
                ent_coef_final=0.01,
                vf_coef=0.5,
                max_grad_norm=0.5,

                # Training parameters
                total_timesteps=timesteps,
                random_seeds=[42],  # Single seed for production
                evaluation_frequency=max(1000, timesteps // 10),
                save_frequency=timesteps,

                # Data splits (already split)
                train_ratio=1.0,  # Use provided splits
                validation_ratio=0.0,
                test_ratio=0.0
            )

            # Train the model
            logger.info(f"Training on {len(train_df)} samples, validating on {len(val_df)} samples")

            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                if self._training_system is None:
                    logger.error("Training system not initialized")
                    return False

                results = loop.run_until_complete(
                    self._training_system.run_experiment(config)
                )

                if results and len(results) > 0:
                    best_result = results[0]  # Single seed

                    # Validate performance against success criteria
                    if self._validate_training_results(best_result):
                        # Update our model
                        self.model = PPO.load(best_result.model_path)
                        self.model_path = best_result.model_path

                        logger.info(f"✅ Comprehensive training successful")
                        logger.info(f"   Sharpe Ratio: {best_result.metrics.sharpe_ratio:.4f}")
                        logger.info(f"   Total Return: {best_result.metrics.total_return:.2f}%")
                        logger.info(f"   Max Drawdown: {best_result.metrics.max_drawdown:.2f}%")
                        logger.info(f"   Model path: {best_result.model_path}")

                        return True
                    else:
                        logger.error("Training results do not meet success criteria")
                        return False
                else:
                    logger.error("No training results obtained")
                    return False

            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Comprehensive training failed: {e}")
            logger.error("Full error details:", exc_info=True)
            return False

    def _prepare_training_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Prepare data with proper chronological time-series splits"""
        if df.empty:
            return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

        # Ensure chronological order
        df_sorted = df.sort_values('timestamp').reset_index(drop=True)

        # Calculate split indices (chronological)
        total_samples = len(df_sorted)
        train_end = int(total_samples * 0.7)  # 70% for training
        val_end = int(total_samples * 0.85)   # 15% for validation
        # Remaining 15% for testing

        train_df = df_sorted.iloc[:train_end].copy()
        val_df = df_sorted.iloc[train_end:val_end].copy()
        test_df = df_sorted.iloc[val_end:].copy()

        logger.info(f"Data splits - Train: {len(train_df)}, Val: {len(val_df)}, Test: {len(test_df)}")

        return train_df, val_df, test_df

    def _validate_training_results(self, result) -> bool:
        """Validate training results against success criteria"""
        criteria = self.training_config['success_criteria']

        # Check Sharpe ratio
        if result.metrics.sharpe_ratio < criteria['min_sharpe_ratio']:
            logger.warning(f"Sharpe ratio {result.metrics.sharpe_ratio:.4f} below minimum {criteria['min_sharpe_ratio']}")
            return False

        # Check total return
        if result.metrics.total_return < criteria['min_total_return']:
            logger.warning(f"Total return {result.metrics.total_return:.2f}% below minimum {criteria['min_total_return']}%")
            return False

        # Check maximum drawdown
        if result.metrics.max_drawdown > criteria['max_drawdown']:
            logger.warning(f"Max drawdown {result.metrics.max_drawdown:.2f}% exceeds limit {criteria['max_drawdown']}%")
            return False

        logger.info("✅ Training results meet all success criteria")
        return True

    def _validate_model_performance(self, update_result: UpdateResult) -> bool:
        """Validate incremental model update performance"""
        if not update_result or not update_result.model_updated:
            return False

        # Check improvement threshold
        if update_result.improvement < self.training_config['performance_threshold']:
            logger.warning(f"Performance improvement {update_result.improvement:.4f} below threshold {self.training_config['performance_threshold']}")
            return False

        # Additional validation could include:
        # - Consistency checks
        # - Risk metric validation
        # - Out-of-sample testing

        logger.info("✅ Incremental model update meets performance criteria")
        return True

    def get_training_status(self) -> Dict[str, Any]:
        """Get current training status and configuration"""
        return {
            'training_in_progress': self._training_in_progress,
            'last_training_time': self.last_training_time.isoformat() if self.last_training_time else None,
            'days_since_training': (datetime.now() - self.last_training_time).days if self.last_training_time else None,
            'retrain_interval_days': self.retrain_interval_days,
            'current_model_path': self.model_path,
            'incremental_learning_enabled': self.training_config['incremental_learning_enabled'],
            'training_config': self.training_config,
            'systems_initialized': {
                'db_connection': self._db_connection is not None,
                'training_system': self._training_system is not None,
                'incremental_system': self._incremental_system is not None
            }
        }

    def force_retrain(self, df: pd.DataFrame, timesteps: int = 50000, incremental: bool = True) -> bool:
        """Force model retraining regardless of schedule"""
        logger.info("Force retraining requested")
        return self._train_model(df, timesteps, incremental, force_retrain=True)

    def cleanup_training_resources(self):
        """Cleanup training resources and connections"""
        try:
            if self._db_connection:
                self._db_connection.close()
                self._db_connection = None

            self._training_system = None
            self._incremental_system = None

            logger.info("Training resources cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up training resources: {e}")

    def reset_historical_data(self, historical_data: Optional[pd.DataFrame] = None):
        """
        Reset the historical data to the provided DataFrame or an empty DataFrame if None.
        If the provided historical_data has more than 1000 rows, only keep the most recent 1000.

        Args:
            historical_data (pd.DataFrame, optional): The DataFrame to set as historical data. Defaults to None.
        """
        if historical_data is not None and not historical_data.empty:
            # Keep only the most recent 1000 rows if historical_data is larger
            self.historical_data = historical_data.tail(1000).copy() if len(
                historical_data) > 1000 else historical_data.copy()
        else:
            self.historical_data = pd.DataFrame()

        logger.info(
            f"Historical data reset with {len(self.historical_data)} candles")

    def process_candle(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series, trade_manager: TradeManager, portfolio_manager: PortfolioManager):
        # Update current step and tracking variables
        self.current_price = candle['close_price']
        self.trade_manager = trade_manager
        self.portfolio_manager = portfolio_manager

        # Process the candle with the base strategy
        super().process_candle(candle, previous_candle,
                               minusthree_candle, trade_manager, portfolio_manager)

        # Add the new candle to historical data
        new_data = pd.DataFrame([candle])
        self.historical_data = pd.concat([self.historical_data, new_data])

        # Limit historical data to 1000 most recent candles to prevent memory issues
        if len(self.historical_data) > 1000:
            logger.debug(
                f"Limiting historical data from {len(self.historical_data)} to 1000 most recent candles")
            self.historical_data = self.historical_data.iloc[-1000:].reset_index(
                drop=True)

        # Check if incremental retraining is needed (non-blocking)
        self._check_and_schedule_retraining()

    def should_enter_trade(self, candle: pd.Series, previous_candle: pd.Series, minusthree_candle: pd.Series) -> bool:
        """
        Determine if a trade should be entered based on the model's prediction.
        Uses unified filtering logic that matches training environment behavior.
        previous_candle and minusthree_candle parameters are required by interface but not used
        """
        # Suppress unused parameter warnings
        _ = (previous_candle, minusthree_candle)
        if self.model is None:
            return False

        # Get model prediction
        observation = self._prepare_observation(candle)
        action_array, _ = self.model.predict(observation, deterministic=True)
        action = self.extract_action_from_prediction(action_array)

        # Apply unified filtering logic (same as used in training)
        return self._apply_entry_filters(candle, action)

    def should_exit_trade(self, candle: pd.Series, previous_candle: pd.Series) -> bool:
        """
        Determine if a trade should be exited based on the model's prediction.
        Uses unified filtering logic that matches training environment behavior.
        previous_candle parameter is required by interface but not used
        """
        # Suppress unused parameter warning
        _ = previous_candle
        if self.model is None:
            return False

        # Get model prediction
        observation = self._prepare_observation(candle)
        action_array, _ = self.model.predict(observation, deterministic=True)
        action = self.extract_action_from_prediction(action_array)

        # Apply unified filtering logic (same as used in training)
        return self._apply_exit_filters(candle, action)

    def _prepare_observation(self, candle: pd.Series) -> np.ndarray:
        """Use TradingEnv's logic to build the observation"""
        # Create a one-row DataFrame from the candle
        df = pd.DataFrame([candle])
        env = TradingEnv(df, portfolio_manager=self.portfolio_manager,
                         trade_manager=self.trade_manager)
        # Force current_step to 0 (only one row)
        env.current_step = 0
        obs = env._next_observation()
        if obs is None:
            obs = np.zeros(env._calculate_num_features(), dtype=np.float32)
        return obs.reshape(1, -1)  # Required shape for model.predict

    def save_model(self, path: Optional[str] = None) -> Optional[str]:
        """Save the current model to disk"""
        if self.model is None:
            logger.error("No model to save")
            return None

        if path is None:
            path = f"./models/ppo_{self.pair}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"

        try:
            self.model.save(path)
            logger.info(f"Model saved to {path}")
            return path
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return None

    def load_model(self, path: str) -> bool:
        """Load a model from disk"""
        try:
            self.model = PPO.load(path)
            self.model_path = path
            logger.info(f"Loaded PPO model from {path}")
            return True
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            return False

    def _check_and_schedule_retraining(self):
        """Check if retraining is needed and schedule it in background (non-blocking)"""
        try:
            # Only check if we have sufficient data and training is not in progress
            if (len(self.historical_data) < 500 or
                self._training_in_progress or
                not self.training_config['incremental_learning_enabled']):
                return

            # Check if retraining is needed
            if self._should_retrain():
                logger.info("Scheduling background model retraining")

                # Schedule retraining in a separate thread to avoid blocking trading
                def background_retrain():
                    try:
                        # Use a larger dataset for retraining (get more historical data)
                        extended_data = self._get_extended_historical_data()
                        if not extended_data.empty:
                            success = self._train_model(
                                extended_data,
                                timesteps=30000,  # Reduced for faster retraining
                                incremental=True,
                                force_retrain=False
                            )
                            if success:
                                logger.info("✅ Background retraining completed successfully")
                            else:
                                logger.warning("⚠️ Background retraining failed")
                        else:
                            logger.warning("Insufficient extended data for retraining")
                    except Exception as e:
                        logger.error(f"Background retraining error: {e}")

                # Start background thread
                if self.training_config['thread_safe_updates']:
                    import threading
                    retrain_thread = threading.Thread(target=background_retrain, daemon=True)
                    retrain_thread.start()

        except Exception as e:
            logger.error(f"Error in retraining check: {e}")

    def _get_extended_historical_data(self) -> pd.DataFrame:
        """Get extended historical data for retraining (beyond the 1000 candle limit)"""
        try:
            if not self._db_connection:
                if not self._initialize_training_systems():
                    return pd.DataFrame()

            # Get data for the training window (18 months by default)
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=self.training_config['training_window_months'] * 30)

            # Use the historical data feed to get extended data
            from app.scripts.historical_data_feed import HistoricalDataFeed

            # Create a dummy preprocess function for data loading
            def dummy_preprocess(df):
                return df

            if self._db_connection is None:
                logger.error("Database connection not available")
                return pd.DataFrame()

            data_feed = HistoricalDataFeed(
                db=self._db_connection,
                pair=self.pair,
                start_date=start_date,
                end_date=end_date,
                preprocess_func=dummy_preprocess
            )

            # Run async method in sync context
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                loop.run_until_complete(data_feed.connect())
                extended_data = data_feed.candles.copy() if data_feed.candles is not None else pd.DataFrame()

                # Preprocess the data using our existing logic
                if not extended_data.empty:
                    extended_data = self._preprocess_data_for_training(extended_data)

                logger.info(f"Retrieved {len(extended_data)} candles for retraining")
                return extended_data

            finally:
                loop.close()

        except Exception as e:
            logger.error(f"Error getting extended historical data: {e}")
            return pd.DataFrame()

    def _preprocess_data_for_training(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess data for training (add indicators, clean data, etc.)"""
        try:
            if df.empty:
                return df

            # Ensure required columns exist
            required_columns = ['timestamp', 'open_price', 'high_price', 'low_price', 'close_price', 'volume']
            if not all(col in df.columns for col in required_columns):
                logger.error("Missing required columns in data")
                return pd.DataFrame()

            # Sort by timestamp to ensure chronological order
            df = df.sort_values('timestamp').reset_index(drop=True)

            # Add technical indicators using the existing indicator calculator
            # Use the static methods from IndicatorCalculator
            df['ema_7'] = IndicatorCalculator.calculate_ema(df, 7, 'close_price')
            df['ema_21'] = IndicatorCalculator.calculate_ema(df, 21, 'close_price')
            df['rsi_14'] = IndicatorCalculator.calculate_rsi(df, 14, 'close_price')

            # MACD returns tuple, so handle it properly
            macd, macd_signal, macd_hist = IndicatorCalculator.calculate_macd(df, 12, 26, 9, 'close_price')
            df['macd'] = macd
            df['macd_signal'] = macd_signal
            df['macd_hist'] = macd_hist

            # Bollinger Bands
            bb_upper, bb_middle, bb_lower = IndicatorCalculator.calculate_bollinger_bands(df, 20, 2, 'close_price')
            df['bb_upper'] = bb_upper
            df['bb_middle'] = bb_middle
            df['bb_lower'] = bb_lower

            # Remove any rows with NaN values that might have been introduced
            df = df.dropna().reset_index(drop=True)

            logger.info(f"Preprocessed data: {len(df)} samples with {len(df.columns)} features")
            return df

        except Exception as e:
            logger.error(f"Error preprocessing data: {e}")
            return pd.DataFrame()

    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive information about the current model and training status"""
        return {
            'model_loaded': self.model is not None,
            'model_path': self.model_path,
            'training_status': self.get_training_status(),
            'historical_data_size': len(self.historical_data),
            'last_action': self.last_action,
            'pair': self.pair,
            'retrain_interval_days': self.retrain_interval_days,
            'comprehensive_training_enabled': True,
            'risk_management_integration': {
                'stop_loss': self.stop_loss,
                'take_profit_levels': self.take_profit_levels,
                'max_investment': self.max_investment,
                'min_investment': self.min_investment
            }
        }
