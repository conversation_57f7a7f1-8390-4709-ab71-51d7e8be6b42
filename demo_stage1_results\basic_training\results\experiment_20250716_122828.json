[{"config": {"pair": "SOL/USD", "start_date": "2024-01-01 00:00:00+00:00", "end_date": "2024-07-01 00:00:00+00:00", "train_ratio": 0.7, "validation_ratio": 0.2, "test_ratio": 0.1, "learning_rate_initial": 0.0003, "learning_rate_final": 5e-05, "n_steps": 1024, "batch_size": 128, "n_epochs": 10, "gamma": 0.97, "gae_lambda": 0.95, "clip_range": 0.2, "ent_coef_initial": 0.05, "ent_coef_final": 0.01, "vf_coef": 0.5, "max_grad_norm": 0.5, "total_timesteps": 10000, "random_seeds": [42, 123, 456], "evaluation_frequency": 2000, "save_frequency": 5000}, "seed": 42, "metrics": {"total_return": 4.749800000000026, "sharpe_ratio": 1.2660486042334986, "sortino_ratio": NaN, "calmar_ratio": 4749800000.000026, "max_drawdown": 0.0, "volatility": 2.136294529886644, "win_rate": 0.0, "avg_trade_duration": 0.0, "trade_frequency": 0.0, "var_95": 0.0, "final_net_worth": 5237.490000000002}, "model_path": "demo_stage1_results\\basic_training\\models\\ppo_SOLUSD_20250716_122658_seed_42.zip", "training_time": 39.511489152908325}, {"config": {"pair": "SOL/USD", "start_date": "2024-01-01 00:00:00+00:00", "end_date": "2024-07-01 00:00:00+00:00", "train_ratio": 0.7, "validation_ratio": 0.2, "test_ratio": 0.1, "learning_rate_initial": 0.0003, "learning_rate_final": 5e-05, "n_steps": 1024, "batch_size": 128, "n_epochs": 10, "gamma": 0.97, "gae_lambda": 0.95, "clip_range": 0.2, "ent_coef_initial": 0.05, "ent_coef_final": 0.01, "vf_coef": 0.5, "max_grad_norm": 0.5, "total_timesteps": 10000, "random_seeds": [42, 123, 456], "evaluation_frequency": 2000, "save_frequency": 5000}, "seed": 123, "metrics": {"total_return": 0.0, "sharpe_ratio": 0.0, "sortino_ratio": NaN, "calmar_ratio": 0.0, "max_drawdown": 0.0, "volatility": 0.0, "win_rate": 0.0, "avg_trade_duration": 0.0, "trade_frequency": 0.0, "var_95": 0.0, "final_net_worth": 5000.0}, "model_path": "demo_stage1_results\\basic_training\\models\\ppo_SOLUSD_20250716_122743_seed_123.zip", "training_time": 44.48045539855957}, {"config": {"pair": "SOL/USD", "start_date": "2024-01-01 00:00:00+00:00", "end_date": "2024-07-01 00:00:00+00:00", "train_ratio": 0.7, "validation_ratio": 0.2, "test_ratio": 0.1, "learning_rate_initial": 0.0003, "learning_rate_final": 5e-05, "n_steps": 1024, "batch_size": 128, "n_epochs": 10, "gamma": 0.97, "gae_lambda": 0.95, "clip_range": 0.2, "ent_coef_initial": 0.05, "ent_coef_final": 0.01, "vf_coef": 0.5, "max_grad_norm": 0.5, "total_timesteps": 10000, "random_seeds": [42, 123, 456], "evaluation_frequency": 2000, "save_frequency": 5000}, "seed": 456, "metrics": {"total_return": 0.0, "sharpe_ratio": 0.0, "sortino_ratio": NaN, "calmar_ratio": 0.0, "max_drawdown": 0.0, "volatility": 0.0, "win_rate": 0.0, "avg_trade_duration": 0.0, "trade_frequency": 0.0, "var_95": 0.0, "final_net_worth": 5000.0}, "model_path": "demo_stage1_results\\basic_training\\models\\ppo_SOLUSD_20250716_122827_seed_456.zip", "training_time": 42.85334348678589}]