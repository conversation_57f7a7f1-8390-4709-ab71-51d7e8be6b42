#!/usr/bin/env python3
"""
Execute Complete PPO Trading Model Training Pipeline

This script runs the comprehensive training pipeline with detailed results
and analysis for the SOL/USD trading pair.
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone

# Add project root to path
project_root = os.path.abspath('.')
sys.path.append(project_root)

from app.training.training_orchestrator import TrainingOrchestrator
from app.training.comprehensive_training_system import TrainingConfig
from app.db.db_executor import DatabaseExecutor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('complete_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


async def execute_complete_pipeline():
    """Execute the complete training pipeline with detailed results"""
    
    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'), 
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        logger.info('='*80)
        logger.info('EXECUTING COMPLETE PPO TRADING MODEL TRAINING PIPELINE')
        logger.info('='*80)
        logger.info('Pipeline Configuration:')
        logger.info('  - Trading Pair: SOL/USD')
        logger.info('  - Hyperparameter Optimization: ENABLED')
        logger.info('  - Walk-Forward Validation: ENABLED')
        logger.info('  - Multiple Random Seeds: ENABLED')
        logger.info('  - Incremental Learning Setup: ENABLED')
        logger.info('='*80)
        
        # Create orchestrator with dedicated output directory
        orchestrator = TrainingOrchestrator(db, './complete_pipeline_results')
        
        # Execute complete pipeline with all stages
        results = await orchestrator.run_complete_training_pipeline(
            pair='SOL/USD',
            optimize_hyperparams=True,  # Enable hyperparameter optimization
            run_validation=True         # Enable walk-forward validation
        )
        
        logger.info('='*80)
        logger.info('PIPELINE EXECUTION COMPLETED')
        logger.info('='*80)
        
        # Print detailed results summary
        if results['pipeline_status'] == 'completed':
            logger.info('✅ PIPELINE SUCCESSFUL')
            
            summary = results.get('summary', {})
            best_model_path = summary.get('best_model_path', 'Not available')
            ready_for_trading = summary.get('ready_for_live_trading', False)
            
            logger.info(f'Best Model Path: {best_model_path}')
            logger.info(f'Ready for Live Trading: {ready_for_trading}')
            
            # Print stage-by-stage results
            logger.info('\nSTAGE RESULTS:')
            logger.info('-' * 40)
            
            if results.get('optimization', {}).get('completed'):
                logger.info('✅ Hyperparameter Optimization: COMPLETED')
                opt_config = results['optimization'].get('best_config', {})
                if opt_config:
                    logger.info(f'   Best Learning Rate Initial: {opt_config.get("learning_rate_initial", "N/A")}')
                    logger.info(f'   Best Learning Rate Final: {opt_config.get("learning_rate_final", "N/A")}')
                    logger.info(f'   Best N Steps: {opt_config.get("n_steps", "N/A")}')
                    logger.info(f'   Best Batch Size: {opt_config.get("batch_size", "N/A")}')
                    logger.info(f'   Best N Epochs: {opt_config.get("n_epochs", "N/A")}')
            else:
                logger.info('⚠️  Hyperparameter Optimization: SKIPPED')
            
            if results.get('initial_training', {}).get('completed'):
                logger.info('✅ Initial Training: COMPLETED')
                training_path = results['initial_training'].get('best_model_path', 'N/A')
                logger.info(f'   Best Model: {training_path}')
            else:
                logger.info('❌ Initial Training: FAILED')
                
            if results.get('validation', {}).get('completed'):
                logger.info('✅ Walk-Forward Validation: COMPLETED')
                validation_analysis = results['validation']['analysis']
                time_analysis = validation_analysis.get('time_analysis', {})
                stats = validation_analysis.get('statistics', {})
                
                consistency = time_analysis.get('consistency_ratio', 0)
                total_periods = time_analysis.get('total_periods', 0)
                positive_periods = time_analysis.get('positive_periods', 0)
                
                logger.info(f'   Total Periods Tested: {total_periods}')
                logger.info(f'   Positive Periods: {positive_periods}')
                logger.info(f'   Consistency Ratio: {consistency:.2%}')
                
                if 'sharpe_ratio' in stats:
                    sharpe_stats = stats['sharpe_ratio']
                    logger.info(f'   Average Sharpe Ratio: {sharpe_stats.get("mean", 0):.4f} ± {sharpe_stats.get("std", 0):.4f}')
                
                if 'total_return' in stats:
                    return_stats = stats['total_return']
                    logger.info(f'   Average Total Return: {return_stats.get("mean", 0):.2f}% ± {return_stats.get("std", 0):.2f}%')
                
                if 'max_drawdown' in stats:
                    dd_stats = stats['max_drawdown']
                    logger.info(f'   Average Max Drawdown: {dd_stats.get("mean", 0):.2f}% ± {dd_stats.get("std", 0):.2f}%')
                    
            else:
                logger.info('❌ Walk-Forward Validation: FAILED')
                
            if results.get('incremental_setup', {}).get('completed'):
                logger.info('✅ Incremental Learning Setup: COMPLETED')
                inc_config = results['incremental_setup'].get('config', {})
                logger.info(f'   Update Frequency: {inc_config.get("update_frequency_days", "N/A")} days')
                logger.info(f'   Training Window: {inc_config.get("training_window_months", "N/A")} months')
                logger.info(f'   Performance Threshold: {inc_config.get("performance_threshold", "N/A")}')
            else:
                logger.info('❌ Incremental Learning Setup: FAILED')
                
            logger.info('\n' + '='*80)
            logger.info('PRODUCTION READINESS ASSESSMENT')
            logger.info('='*80)
            
            if ready_for_trading:
                logger.info('🚀 MODEL IS READY FOR LIVE TRADING DEPLOYMENT')
                logger.info('   ✅ Training completed successfully')
                logger.info('   ✅ Validation shows consistent performance')
                logger.info('   ✅ Incremental learning configured')
                logger.info('   ✅ Model backup and versioning ready')
            else:
                logger.info('⚠️  MODEL REQUIRES ADDITIONAL VALIDATION')
                logger.info('   Please review validation results before deployment')
                
        else:
            logger.error('❌ PIPELINE FAILED')
            error_msg = results.get('error', 'Unknown error')
            logger.error(f'Error: {error_msg}')
            
        return results
            
    except Exception as e:
        logger.error(f'Pipeline execution failed: {e}')
        logger.error('Full error details:', exc_info=True)
        raise
    finally:
        db.close()


async def demonstrate_incremental_learning(best_model_path: str):
    """Demonstrate incremental learning setup and execution"""
    
    logger.info('\n' + '='*80)
    logger.info('DEMONSTRATING INCREMENTAL LEARNING CAPABILITIES')
    logger.info('='*80)
    
    # Initialize database connection
    db = DatabaseExecutor(
        db_name=os.getenv('DB_NAME', 'trading_db'),
        db_user=os.getenv('DB_USER', 'postgres'), 
        db_password=os.getenv('DB_PASSWORD', 'password'),
        db_host=os.getenv('DB_HOST', 'localhost'),
        db_port=int(os.getenv('DB_PORT', '5432'))
    )
    
    try:
        orchestrator = TrainingOrchestrator(db, './incremental_demo_results')
        
        # Setup incremental learning
        incremental_config = await orchestrator.setup_incremental_learning(
            best_model_path, "SOL/USD"
        )
        
        logger.info('Incremental Learning Configuration:')
        logger.info(f'  Model Path: {incremental_config.model_path}')
        logger.info(f'  Update Frequency: {incremental_config.update_frequency_days} days')
        logger.info(f'  Training Window: {incremental_config.training_window_months} months')
        logger.info(f'  Validation Window: {incremental_config.validation_window_months} months')
        logger.info(f'  Performance Threshold: {incremental_config.performance_threshold}')
        
        # Simulate incremental update check
        logger.info('\nChecking for incremental updates...')
        update_needed = await orchestrator.incremental_system.check_for_updates(incremental_config)
        
        if update_needed:
            logger.info('✅ Incremental update is needed')
            logger.info('   (In production, this would trigger automatic model update)')
        else:
            logger.info('ℹ️  No incremental update needed at this time')
            
        logger.info('\n🎯 INCREMENTAL LEARNING READY FOR PRODUCTION')
        
    except Exception as e:
        logger.error(f'Incremental learning demonstration failed: {e}')
    finally:
        db.close()


async def main():
    """Main execution function"""
    
    start_time = datetime.now()
    logger.info(f'Pipeline execution started at: {start_time}')
    
    try:
        # Execute complete pipeline
        results = await execute_complete_pipeline()
        
        # If successful, demonstrate incremental learning
        if results.get('pipeline_status') == 'completed':
            best_model_path = results.get('summary', {}).get('best_model_path')
            if best_model_path:
                await demonstrate_incremental_learning(best_model_path)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info('\n' + '='*80)
        logger.info('PIPELINE EXECUTION SUMMARY')
        logger.info('='*80)
        logger.info(f'Start Time: {start_time}')
        logger.info(f'End Time: {end_time}')
        logger.info(f'Total Duration: {duration}')
        logger.info(f'Status: {results.get("pipeline_status", "unknown").upper()}')
        logger.info('='*80)
        
    except Exception as e:
        logger.error(f'Main execution failed: {e}')
        raise


if __name__ == "__main__":
    asyncio.run(main())
