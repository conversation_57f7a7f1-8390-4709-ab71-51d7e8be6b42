#!/usr/bin/env python3
"""
Test the training system components individually
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone

# Add project root to path
project_root = os.path.abspath('.')
sys.path.append(project_root)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_database_connection():
    """Test database connection"""
    try:
        from app.db.db_executor import DatabaseExecutor
        
        db = DatabaseExecutor(
            db_name=os.getenv('DB_NAME', 'trading_db'),
            db_user=os.getenv('DB_USER', 'postgres'), 
            db_password=os.getenv('DB_PASSWORD', 'password'),
            db_host=os.getenv('DB_HOST', 'localhost'),
            db_port=int(os.getenv('DB_PORT', '5432'))
        )
        
        # Test basic query
        result = db.execute_select("SELECT 1 as test", ())
        logger.info(f"Database connection test: {result}")
        
        # Test candles table
        candles_count = db.execute_select(
            "SELECT COUNT(*) FROM candles WHERE pair = %s", 
            ('SOL/USD',)
        )
        logger.info(f"SOL/USD candles available: {candles_count[0][0] if candles_count else 0}")
        
        db.close()
        return True
        
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False

async def test_training_config():
    """Test training configuration"""
    try:
        from app.training.comprehensive_training_system import TrainingConfig
        
        config = TrainingConfig(
            pair="SOL/USD",
            start_date=datetime(2023, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2025, 7, 1, tzinfo=timezone.utc),
            total_timesteps=1000,  # Very small for testing
            random_seeds=[42]  # Single seed for testing
        )
        
        logger.info("Training configuration created successfully")
        logger.info(f"Pair: {config.pair}")
        logger.info(f"Learning rate initial: {config.learning_rate_initial}")
        logger.info(f"N epochs: {config.n_epochs}")
        logger.info(f"Batch size: {config.batch_size}")
        logger.info(f"N steps: {config.n_steps}")
        
        return True
        
    except Exception as e:
        logger.error(f"Training configuration failed: {e}")
        return False

async def test_data_loading():
    """Test data loading functionality"""
    try:
        from app.db.db_executor import DatabaseExecutor
        from app.training.comprehensive_training_system import ComprehensiveTrainingSystem, TrainingConfig
        
        db = DatabaseExecutor(
            db_name=os.getenv('DB_NAME', 'trading_db'),
            db_user=os.getenv('DB_USER', 'postgres'), 
            db_password=os.getenv('DB_PASSWORD', 'password'),
            db_host=os.getenv('DB_HOST', 'localhost'),
            db_port=int(os.getenv('DB_PORT', '5432'))
        )
        
        training_system = ComprehensiveTrainingSystem(db, "./test_output")
        
        config = TrainingConfig(
            pair="SOL/USD",
            start_date=datetime(2024, 1, 1, tzinfo=timezone.utc),
            end_date=datetime(2024, 6, 1, tzinfo=timezone.utc),
            total_timesteps=1000,
            random_seeds=[42]
        )
        
        logger.info("Testing data loading...")
        train_df, val_df, test_df = await training_system.load_and_split_data(config)
        
        logger.info(f"Training data: {len(train_df)} samples")
        logger.info(f"Validation data: {len(val_df)} samples") 
        logger.info(f"Test data: {len(test_df)} samples")
        
        if len(train_df) > 0:
            logger.info(f"Training data columns: {list(train_df.columns)}")
            logger.info(f"Training data date range: {train_df['timestamp'].min()} to {train_df['timestamp'].max()}")
        
        db.close()
        return len(train_df) > 0
        
    except Exception as e:
        logger.error(f"Data loading test failed: {e}")
        return False

async def test_simple_training():
    """Test simple training with minimal configuration"""
    try:
        from app.db.db_executor import DatabaseExecutor
        from app.training.comprehensive_training_system import ComprehensiveTrainingSystem, TrainingConfig
        
        db = DatabaseExecutor(
            db_name=os.getenv('DB_NAME', 'trading_db'),
            db_user=os.getenv('DB_USER', 'postgres'), 
            db_password=os.getenv('DB_PASSWORD', 'password'),
            db_host=os.getenv('DB_HOST', 'localhost'),
            db_port=int(os.getenv('DB_PORT', '5432'))
        )
        
        training_system = ComprehensiveTrainingSystem(db, "./test_training_output")
        
        # Very minimal config for testing
        config = TrainingConfig(
            pair="SOL/USD",
            start_date=datetime(2024, 6, 1, tzinfo=timezone.utc),
            end_date=datetime(2024, 7, 1, tzinfo=timezone.utc),
            total_timesteps=500,  # Very small for quick test
            random_seeds=[42],    # Single seed
            evaluation_frequency=100,
            save_frequency=500
        )
        
        logger.info("Starting simple training test...")
        results = await training_system.run_experiment(config)
        
        if results:
            logger.info(f"Training completed! {len(results)} models trained")
            best_result = results[0]
            logger.info(f"Sharpe ratio: {best_result.metrics.sharpe_ratio:.4f}")
            logger.info(f"Total return: {best_result.metrics.total_return:.2f}%")
            logger.info(f"Model path: {best_result.model_path}")
        else:
            logger.error("No training results obtained")
            
        db.close()
        return len(results) > 0 if results else False
        
    except Exception as e:
        logger.error(f"Simple training test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("="*60)
    logger.info("TESTING TRAINING SYSTEM COMPONENTS")
    logger.info("="*60)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Training Configuration", test_training_config),
        ("Data Loading", test_data_loading),
        ("Simple Training", test_simple_training)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n--- Testing {test_name} ---")
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name}: ❌ FAILED - {e}")
    
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    logger.info(f"\nOverall Status: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
